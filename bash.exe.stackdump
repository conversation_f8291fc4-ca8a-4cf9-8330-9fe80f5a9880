Stack trace:
Frame         Function      Args
0007FFFF3440  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF2340) msys-2.0.dll+0x1FE8E
0007FFFF3440  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF3718) msys-2.0.dll+0x67F9
0007FFFF3440  000210046832 (000210286019, 0007FFFF32F8, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF3440  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF3440  000210068E24 (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF3720  00021006A225 (0007FFFF3450, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFEC1FC0000 ntdll.dll
7FFEC13A0000 KERNEL32.DLL
7FFEBF640000 KERNELBASE.dll
7FFEC1AC0000 USER32.dll
7FFEBF260000 win32u.dll
7FFEC0A40000 GDI32.dll
000210040000 msys-2.0.dll
7FFEBFB90000 gdi32full.dll
7FFEBF590000 msvcp_win.dll
7FFEBF110000 ucrtbase.dll
7FFEBFD70000 advapi32.dll
7FFEC1E60000 msvcrt.dll
7FFEBFFE0000 sechost.dll
7FFEC1C90000 RPCRT4.dll
7FFEBE7A0000 CRYPTBASE.DLL
7FFEBFAF0000 bcryptPrimitives.dll
7FFEBFFA0000 IMM32.DLL
