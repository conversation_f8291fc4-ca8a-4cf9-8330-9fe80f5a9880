﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>Enregistrer</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>Annuler</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>Fermer</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>Appliquer</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Oui</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>Non</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>Options</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Erreur</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>Commencer</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>Arrêter</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>Connecter</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>Déconnecter</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>Activé</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>Désactivé</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>Démo</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>Tableau de bord</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>Historique</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>Filtré</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>Filtre</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>Tous</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>Sélectionner</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>Commande</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>Utilisateur</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>Éditer la liste</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>Utilisateurs</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Nom</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>Êtes-vous sûr ?</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>Europe</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>Plus anciens d'abord</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>Plus récents d'abord</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>Commencer</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>Fin</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>Langue</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>Éditer</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value> h</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>Réinitialiser</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>Créer</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>Paramètres système</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>À propos</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>Poser une question</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>Démo</value>
  </data>
  <data name="ValidationFieldRequired" xml:space="preserve">
    <value>Le champ {0} n'est pas rempli</value>
  </data>
  <data name="AuthForgotPasswordQuestion" xml:space="preserve">
    <value>Mot de passe oublié ?</value>
  </data>
  <data name="AuthLogin" xml:space="preserve">
    <value>Se connecter</value>
  </data>
  <data name="AuthRememberMe" xml:space="preserve">
    <value>Se souvenir de moi</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>Mot de passe</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>Ajouter</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>Rechercher</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>Nom complet</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>Gestionnaire</value>
  </data>
  <data name="Managers" xml:space="preserve">
    <value>Gestionnaires</value>
  </data>
  <data name="Teacher" xml:space="preserve">
    <value>Enseignant</value>
  </data>
  <data name="Teachers" xml:space="preserve">
    <value>Enseignants</value>
  </data>
  <data name="Leads" xml:space="preserve">
    <value>Prospects</value>
  </data>
  <data name="Words" xml:space="preserve">
    <value>Mots</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>Mot</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>Groupe</value>
  </data>
  <data name="Groups" xml:space="preserve">
    <value>Groupes</value>
  </data>
  <data name="Offices" xml:space="preserve">
    <value>Bureaux</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>Bureau</value>
  </data>
  <data name="Translation" xml:space="preserve">
    <value>Traduction</value>
  </data>
  <data name="Difficulty" xml:space="preserve">
    <value>Difficulté</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>Région</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>Régions</value>
  </data>
  <data name="Interaction" xml:space="preserve">
    <value>Interaction</value>
  </data>
  <data name="Shop" xml:space="preserve">
    <value>Boutique</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>Accueil</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>Événements</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>Événement</value>
  </data>
  <data name="Pushes" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="Push" xml:space="preserve">
    <value>Notification</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>Commandes</value>
  </data>
  <data name="OrderBy" xml:space="preserve">
    <value>Trier par</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>Produits</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>Produit</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>Se déconnecter</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>Titre</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>Quantité</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Prix</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>Statut</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>Téléphone</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>Anniversaire</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>Solde</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>Nom de famille</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>Prénom</value>
  </data>
  <data name="PastDue" xml:space="preserve">
    <value>En retard</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Adresse</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>Notes</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Jours</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>Heure</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Courriel</value>
  </data>
  <data name="Messenger" xml:space="preserve">
    <value>Messagerie</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>Description</value>
  </data>
  <data name="ValidationRequired" xml:space="preserve">
    <value>Champ requis</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>Type</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>Texte</value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>Deuxième prénom</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Succès !</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>Demande</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>Demandes</value>
  </data>
  <data name="Edited" xml:space="preserve">
    <value>Édité</value>
  </data>
  <data name="TitleDesk" xml:space="preserve">
    <value>Panneau d'administration</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>Caméra</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>Caméras</value>
  </data>
  <data name="ConfirmationNeeded" xml:space="preserve">
    <value>Confirmation requise</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>Êtes-vous sûr de vouloir supprimer cette entrée ?</value>
  </data>
  <data name="ConfirmAction" xml:space="preserve">
    <value>Veuillez confirmer cette action !</value>
  </data>
  <data name="Unset" xml:space="preserve">
    <value>Non défini</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>Aucun</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>Version</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>Supprimer</value>
  </data>
  <data name="StudyPlanType_Other" xml:space="preserve">
    <value>Autre</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>Bienvenue</value>
  </data>
  <data name="RowsPerPage" xml:space="preserve">
    <value>Lignes par page</value>
  </data>
  <data name="NoRights" xml:space="preserve">
    <value>Non autorisé</value>
  </data>
  <data name="OrderStatus_Received" xml:space="preserve">
    <value>En traitement</value>
  </data>
  <data name="OrderStatus_Issued" xml:space="preserve">
    <value>Livré au client</value>
  </data>
  <data name="OrderStatus_Sent" xml:space="preserve">
    <value>Envoyé au bureau</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>Montant</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="ChangeBalance" xml:space="preserve">
    <value>Modifier le solde</value>
  </data>
  <data name="MyProfile" xml:space="preserve">
    <value>Mon profil</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>Réinitialiser le mot de passe</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>Nom d'utilisateur</value>
  </data>
  <data name="WrongPassword" xml:space="preserve">
    <value>Nom d'utilisateur ou mot de passe incorrect</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>Appuyez sur Retour une fois de plus pour quitter...</value>
  </data>
  <data name="BtnShare" xml:space="preserve">
    <value>Partager</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>Retour</value>
  </data>
  <data name="StatePaused" xml:space="preserve">
    <value>En pause</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="BtnQuit" xml:space="preserve">
    <value>Quitter</value>
  </data>
  <data name="BtnContinue" xml:space="preserve">
    <value>Continuer</value>
  </data>
  <data name="BtnPlayAgain" xml:space="preserve">
    <value>Rejouer</value>
  </data>
  <data name="StartGame" xml:space="preserve">
    <value>Commencer le jeu</value>
  </data>
  <data name="Score" xml:space="preserve">
    <value>Score</value>
  </data>
  <data name="MessageGameOver" xml:space="preserve">
    <value>😔 Jeu terminé ! 💔
Score final : {0}
Prêt pour un autre round ?</value>
  </data>
  <data name="MessageLevelComplete" xml:space="preserve">
    <value>🎯 Niveau {0} terminé ! 🎉
Score : {1}
Préparez-vous pour le niveau {2} !</value>
  </data>
  <data name="MessageWelcome" xml:space="preserve">
    <value>💥 Bienvenue dans Breakout ! 🏓
🏆 Détruisez toutes les briques, la victoire vous attend !</value>
  </data>
  <data name="MessageWelcomeDesktop" xml:space="preserve">
    <value>💥 Bienvenue dans Breakout ! 🏓
Utilisez la souris/clavier pour contrôler.
🏆 Détruisez toutes les briques, la victoire vous attend !</value>
  </data>
  <data name="MessageGameComplete" xml:space="preserve">
    <value>🎉 Incroyable ! Vous avez conquis tous les niveaux ! 🎉
Score final : {0}
Vous êtes un maître de Breakout !</value>
  </data>
</root>