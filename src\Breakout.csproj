<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>

    <!--<TargetFrameworks>net9.0-windows10.0.19041.0</TargetFrameworks>-->

    <TargetFrameworks>net9.0-android;net9.0-ios;net9.0-maccatalyst</TargetFrameworks>
    <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>

    <OutputType>Exe</OutputType>
    <RootNamespace>Breakout</RootNamespace>
    <UseMaui>true</UseMaui>
    <SingleProject>true</SingleProject>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- Display name -->
    <ApplicationTitle>Breakout</ApplicationTitle>

    <UseNuget>true</UseNuget>
    
    <!-- App Identifier -->
    <ApplicationId>com.mauigame.breakout</ApplicationId>
    <ApplicationIdGuid>230e07b0-ebc2-4fde-9ac8-074d3c3d69a9</ApplicationIdGuid>

    <!-- Versions -->
    <ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
    <ApplicationVersion>1</ApplicationVersion>

    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
    <WindowsPackageType>None</WindowsPackageType>
  </PropertyGroup>

  <!--production-->
  <ItemGroup Condition="'$(UseNuget)' == 'true'">
    <PackageReference Include="DrawnUi.Maui.Game" Version="1.6.2.7" />
  </ItemGroup>


  <ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

  <ItemGroup>
    <Compile Remove="Game\Internals\MauiGame.cs" />
  </ItemGroup>

  <PropertyGroup Condition="$(TargetFramework.Contains('ios')) == true">
    <ProvisioningType>manual</ProvisioningType>
  </PropertyGroup>

  <ItemGroup Condition="$(Configuration) == 'Debug'">
    <!--https://github.com/BretJohnson/hot-preview-->
    <PackageReference Include="HotPreview.App.Maui" Version="0.13.27" />
  </ItemGroup>

  <ItemGroup>

    <!--can replace this with usual plugin if PR is merged-->
    <PackageReference Include="AppoMobi.Preview.Plugin.Maui.Audio" Version="3.0.2.1-pre1" />
    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
    <!--<PackageReference Include="AppoMobi.Maui.DrawnUi" Version="1.3.60.1" />-->
	</ItemGroup>

  <!--for development-->
  <ItemGroup Condition="'$(UseNuget)' != 'true'">
    <ProjectReference Include="..\..\DrawnUi.Maui\src\Maui\DrawnUi\DrawnUi.Maui.csproj" />
    <ProjectReference Include="..\..\DrawnUi.Maui\src\Maui\Addons\DrawnUi.Maui.Game\DrawnUi.Maui.Game.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Resources\Strings\ResStrings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>ResStrings.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Resources\Strings\ResStrings.resx">
      <LastGenOutput>ResStrings.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
  </ItemGroup>

</Project>
