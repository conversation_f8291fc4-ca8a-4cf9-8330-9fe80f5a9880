﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>閉じる</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>適用</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>はい</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>いいえ</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>オプション</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>エラー</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>開始</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>停止</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>接続</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>切断</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>オン</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>オフ</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>デモ</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>ダッシュボード</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>履歴</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>フィルタリング済み</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>フィルタ</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>すべて</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>選択</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>注文</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>ユーザー</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>リストを編集</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>ユーザー</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>名前</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>本当によろしいですか？</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>ヨーロッパ</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>古い順</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>新しい順</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>開始</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>終了</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>言語</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value>時間</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>リセット</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>作成</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>システム設定</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>このアプリについて</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>質問をする</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>デモ</value>
  </data>
  <data name="ValidationFieldRequired" xml:space="preserve">
    <value>{0}フィールドが入力されていません</value>
  </data>
  <data name="AuthForgotPasswordQuestion" xml:space="preserve">
    <value>パスワードを忘れましたか？</value>
  </data>
  <data name="AuthLogin" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="AuthRememberMe" xml:space="preserve">
    <value>ログイン状態を保持</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>追加</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>検索</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>氏名</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>日付</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>管理者</value>
  </data>
  <data name="Managers" xml:space="preserve">
    <value>管理者</value>
  </data>
  <data name="Teacher" xml:space="preserve">
    <value>先生</value>
  </data>
  <data name="Teachers" xml:space="preserve">
    <value>先生</value>
  </data>
  <data name="Leads" xml:space="preserve">
    <value>リード</value>
  </data>
  <data name="Words" xml:space="preserve">
    <value>単語</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>単語</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>グループ</value>
  </data>
  <data name="Groups" xml:space="preserve">
    <value>グループ</value>
  </data>
  <data name="Offices" xml:space="preserve">
    <value>オフィス</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>オフィス</value>
  </data>
  <data name="Translation" xml:space="preserve">
    <value>翻訳</value>
  </data>
  <data name="Difficulty" xml:space="preserve">
    <value>難易度</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>地域</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>地域</value>
  </data>
  <data name="Interaction" xml:space="preserve">
    <value>相互作用</value>
  </data>
  <data name="Shop" xml:space="preserve">
    <value>ショップ</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>ホーム</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>イベント</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>イベント</value>
  </data>
  <data name="Pushes" xml:space="preserve">
    <value>プッシュ通知</value>
  </data>
  <data name="Push" xml:space="preserve">
    <value>プッシュ</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>注文</value>
  </data>
  <data name="OrderBy" xml:space="preserve">
    <value>並び順</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>製品</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>製品</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>ログアウト</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>タイトル</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>数量</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>価格</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>状態</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>電話番号</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>誕生日</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>残高</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>姓</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>名</value>
  </data>
  <data name="PastDue" xml:space="preserve">
    <value>期限切れ</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>住所</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>メモ</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>日</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>時間</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>メール</value>
  </data>
  <data name="Messenger" xml:space="preserve">
    <value>メッセンジャー</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>説明</value>
  </data>
  <data name="ValidationRequired" xml:space="preserve">
    <value>必須フィールド</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>タイプ</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>テキスト</value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>ミドルネーム</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>成功！</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>リクエスト</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>リクエスト</value>
  </data>
  <data name="Edited" xml:space="preserve">
    <value>編集済み</value>
  </data>
  <data name="TitleDesk" xml:space="preserve">
    <value>管理パネル</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>カメラ</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>カメラ</value>
  </data>
  <data name="ConfirmationNeeded" xml:space="preserve">
    <value>確認が必要です</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>このエントリを削除してもよろしいですか？</value>
  </data>
  <data name="ConfirmAction" xml:space="preserve">
    <value>この操作を確認してください！</value>
  </data>
  <data name="Unset" xml:space="preserve">
    <value>未設定</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>なし</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>バージョン</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>削除</value>
  </data>
  <data name="StudyPlanType_Other" xml:space="preserve">
    <value>その他</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>ようこそ</value>
  </data>
  <data name="RowsPerPage" xml:space="preserve">
    <value>ページあたりの行数</value>
  </data>
  <data name="NoRights" xml:space="preserve">
    <value>権限がありません</value>
  </data>
  <data name="OrderStatus_Received" xml:space="preserve">
    <value>処理中</value>
  </data>
  <data name="OrderStatus_Issued" xml:space="preserve">
    <value>クライアントに配信済み</value>
  </data>
  <data name="OrderStatus_Sent" xml:space="preserve">
    <value>オフィスに送信済み</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>金額</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>情報</value>
  </data>
  <data name="ChangeBalance" xml:space="preserve">
    <value>残高を変更</value>
  </data>
  <data name="MyProfile" xml:space="preserve">
    <value>マイプロフィール</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>パスワードをリセット</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>ユーザー名</value>
  </data>
  <data name="WrongPassword" xml:space="preserve">
    <value>ユーザー名またはパスワードが間違っています</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>戻るボタンをもう一度押して終了してください...</value>
  </data>
  <data name="BtnShare" xml:space="preserve">
    <value>共有</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>戻る</value>
  </data>
  <data name="StatePaused" xml:space="preserve">
    <value>一時停止</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="BtnQuit" xml:space="preserve">
    <value>終了</value>
  </data>
  <data name="BtnContinue" xml:space="preserve">
    <value>続行</value>
  </data>
  <data name="BtnPlayAgain" xml:space="preserve">
    <value>もう一度プレイ</value>
  </data>
  <data name="StartGame" xml:space="preserve">
    <value>ゲーム開始</value>
  </data>
  <data name="Score" xml:space="preserve">
    <value>スコア</value>
  </data>
  <data name="MessageGameOver" xml:space="preserve">
    <value>😔 ゲームオーバー！ 💔
最終スコア: {0}
もう一度プレイしますか？</value>
  </data>
  <data name="MessageLevelComplete" xml:space="preserve">
    <value>🎯 レベル{0}クリア！ 🎉
スコア: {1}
レベル{2}の準備をしよう！</value>
  </data>
  <data name="MessageWelcome" xml:space="preserve">
    <value>💥 ブレイクアウトへようこそ！ 🏓
🏆 すべてのブロックを壊して、勝利を掴もう！</value>
  </data>
  <data name="MessageWelcomeDesktop" xml:space="preserve">
    <value>💥 ブレイクアウトへようこそ！ 🏓
マウス/キーボードで操作してください。
🏆 すべてのブロックを壊して、勝利を掴もう！</value>
  </data>
  <data name="MessageGameComplete" xml:space="preserve">
    <value>🎉 素晴らしい！すべてのレベルを制覇しました！ 🎉
最終スコア: {0}
あなたはブレイクアウトマスターです！</value>
  </data>
  <data name="Lives" xml:space="preserve">
    <value>ライフ</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>レベル</value>
  </data>
  <data name="Lev" xml:space="preserve">
    <value>レベル</value>
  </data>
  <data name="Music" xml:space="preserve">
    <value>音楽</value>
  </data>
  <data name="Sounds" xml:space="preserve">
    <value>音</value>
  </data>
</root>