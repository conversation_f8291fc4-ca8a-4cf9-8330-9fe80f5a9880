﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BtnSave" xml:space="preserve">
    <value>저장</value>
  </data>
  <data name="BtnCancel" xml:space="preserve">
    <value>취소</value>
  </data>
  <data name="BtnClose" xml:space="preserve">
    <value>닫기</value>
  </data>
  <data name="BtnApply" xml:space="preserve">
    <value>적용</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>예</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>아니오</value>
  </data>
  <data name="Options" xml:space="preserve">
    <value>옵션</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>오류</value>
  </data>
  <data name="BtnStart" xml:space="preserve">
    <value>시작</value>
  </data>
  <data name="BtnStop" xml:space="preserve">
    <value>중지</value>
  </data>
  <data name="BtnConnect" xml:space="preserve">
    <value>연결</value>
  </data>
  <data name="BtnDisconnect" xml:space="preserve">
    <value>연결 해제</value>
  </data>
  <data name="On" xml:space="preserve">
    <value>켜기</value>
  </data>
  <data name="Off" xml:space="preserve">
    <value>끄기</value>
  </data>
  <data name="VendorTitle" xml:space="preserve">
    <value>데모</value>
  </data>
  <data name="Dashboard" xml:space="preserve">
    <value>대시보드</value>
  </data>
  <data name="History" xml:space="preserve">
    <value>기록</value>
  </data>
  <data name="Filtered" xml:space="preserve">
    <value>필터링됨</value>
  </data>
  <data name="Filter" xml:space="preserve">
    <value>필터</value>
  </data>
  <data name="All" xml:space="preserve">
    <value>모두</value>
  </data>
  <data name="Select" xml:space="preserve">
    <value>선택</value>
  </data>
  <data name="Order" xml:space="preserve">
    <value>주문</value>
  </data>
  <data name="User" xml:space="preserve">
    <value>사용자</value>
  </data>
  <data name="EditList" xml:space="preserve">
    <value>목록 편집</value>
  </data>
  <data name="Users" xml:space="preserve">
    <value>사용자</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>이름</value>
  </data>
  <data name="Deleting" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
    <value>정말로 하시겠습니까?</value>
  </data>
  <data name="Europe" xml:space="preserve">
    <value>유럽</value>
  </data>
  <data name="OrderOldFirst" xml:space="preserve">
    <value>오래된 것 먼저</value>
  </data>
  <data name="OrderNewFirst" xml:space="preserve">
    <value>새로운 것 먼저</value>
  </data>
  <data name="Start" xml:space="preserve">
    <value>시작</value>
  </data>
  <data name="End" xml:space="preserve">
    <value>종료</value>
  </data>
  <data name="Language" xml:space="preserve">
    <value>언어</value>
  </data>
  <data name="Edit" xml:space="preserve">
    <value>편집</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>삭제</value>
  </data>
  <data name="HoursShort" xml:space="preserve">
    <value>시간</value>
  </data>
  <data name="BtnReset" xml:space="preserve">
    <value>재설정</value>
  </data>
  <data name="BtnCreate" xml:space="preserve">
    <value>만들기</value>
  </data>
  <data name="SystemSettings" xml:space="preserve">
    <value>시스템 설정</value>
  </data>
  <data name="AboutApp" xml:space="preserve">
    <value>정보</value>
  </data>
  <data name="BtnQuestion" xml:space="preserve">
    <value>질문하기</value>
  </data>
  <data name="DemoMode" xml:space="preserve">
    <value>데모</value>
  </data>
  <data name="ValidationFieldRequired" xml:space="preserve">
    <value>{0} 필드가 채워지지 않았습니다</value>
  </data>
  <data name="AuthForgotPasswordQuestion" xml:space="preserve">
    <value>비밀번호를 잊으셨나요?</value>
  </data>
  <data name="AuthLogin" xml:space="preserve">
    <value>로그인</value>
  </data>
  <data name="AuthRememberMe" xml:space="preserve">
    <value>로그인 상태 유지</value>
  </data>
  <data name="Password" xml:space="preserve">
    <value>비밀번호</value>
  </data>
  <data name="Add" xml:space="preserve">
    <value>추가</value>
  </data>
  <data name="Search" xml:space="preserve">
    <value>검색</value>
  </data>
  <data name="FullName" xml:space="preserve">
    <value>전체 이름</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>날짜</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>관리자</value>
  </data>
  <data name="Managers" xml:space="preserve">
    <value>관리자</value>
  </data>
  <data name="Teacher" xml:space="preserve">
    <value>선생님</value>
  </data>
  <data name="Teachers" xml:space="preserve">
    <value>선생님</value>
  </data>
  <data name="Leads" xml:space="preserve">
    <value>리드</value>
  </data>
  <data name="Words" xml:space="preserve">
    <value>단어</value>
  </data>
  <data name="Word" xml:space="preserve">
    <value>단어</value>
  </data>
  <data name="Group" xml:space="preserve">
    <value>그룹</value>
  </data>
  <data name="Groups" xml:space="preserve">
    <value>그룹</value>
  </data>
  <data name="Offices" xml:space="preserve">
    <value>사무실</value>
  </data>
  <data name="Office" xml:space="preserve">
    <value>사무실</value>
  </data>
  <data name="Translation" xml:space="preserve">
    <value>번역</value>
  </data>
  <data name="Difficulty" xml:space="preserve">
    <value>난이도</value>
  </data>
  <data name="Region" xml:space="preserve">
    <value>지역</value>
  </data>
  <data name="Regions" xml:space="preserve">
    <value>지역</value>
  </data>
  <data name="Interaction" xml:space="preserve">
    <value>상호작용</value>
  </data>
  <data name="Shop" xml:space="preserve">
    <value>상점</value>
  </data>
  <data name="Home" xml:space="preserve">
    <value>홈</value>
  </data>
  <data name="Events" xml:space="preserve">
    <value>이벤트</value>
  </data>
  <data name="Event" xml:space="preserve">
    <value>이벤트</value>
  </data>
  <data name="Pushes" xml:space="preserve">
    <value>푸시 알림</value>
  </data>
  <data name="Push" xml:space="preserve">
    <value>푸시</value>
  </data>
  <data name="Orders" xml:space="preserve">
    <value>주문</value>
  </data>
  <data name="OrderBy" xml:space="preserve">
    <value>정렬 기준</value>
  </data>
  <data name="Products" xml:space="preserve">
    <value>제품</value>
  </data>
  <data name="Product" xml:space="preserve">
    <value>제품</value>
  </data>
  <data name="LogOut" xml:space="preserve">
    <value>로그아웃</value>
  </data>
  <data name="Title" xml:space="preserve">
    <value>제목</value>
  </data>
  <data name="Quantity" xml:space="preserve">
    <value>수량</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>가격</value>
  </data>
  <data name="Status" xml:space="preserve">
    <value>상태</value>
  </data>
  <data name="PhoneNumber" xml:space="preserve">
    <value>전화번호</value>
  </data>
  <data name="Birthday" xml:space="preserve">
    <value>생일</value>
  </data>
  <data name="Balance" xml:space="preserve">
    <value>잔액</value>
  </data>
  <data name="LastName" xml:space="preserve">
    <value>성</value>
  </data>
  <data name="FirstName" xml:space="preserve">
    <value>이름</value>
  </data>
  <data name="PastDue" xml:space="preserve">
    <value>기한 만료</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>주소</value>
  </data>
  <data name="Notes" xml:space="preserve">
    <value>메모</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>일</value>
  </data>
  <data name="Time" xml:space="preserve">
    <value>시간</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>이메일</value>
  </data>
  <data name="Messenger" xml:space="preserve">
    <value>메신저</value>
  </data>
  <data name="Description" xml:space="preserve">
    <value>설명</value>
  </data>
  <data name="ValidationRequired" xml:space="preserve">
    <value>필수 필드</value>
  </data>
  <data name="Type" xml:space="preserve">
    <value>유형</value>
  </data>
  <data name="Text" xml:space="preserve">
    <value>텍스트</value>
  </data>
  <data name="MiddleName" xml:space="preserve">
    <value>중간 이름</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>성공!</value>
  </data>
  <data name="Request" xml:space="preserve">
    <value>요청</value>
  </data>
  <data name="Requests" xml:space="preserve">
    <value>요청</value>
  </data>
  <data name="Edited" xml:space="preserve">
    <value>편집됨</value>
  </data>
  <data name="TitleDesk" xml:space="preserve">
    <value>관리 패널</value>
  </data>
  <data name="Camera" xml:space="preserve">
    <value>카메라</value>
  </data>
  <data name="Cameras" xml:space="preserve">
    <value>카메라</value>
  </data>
  <data name="ConfirmationNeeded" xml:space="preserve">
    <value>확인이 필요합니다</value>
  </data>
  <data name="ConfirmDelete" xml:space="preserve">
    <value>이 항목을 삭제하시겠습니까?</value>
  </data>
  <data name="ConfirmAction" xml:space="preserve">
    <value>이 작업을 확인해주세요!</value>
  </data>
  <data name="Unset" xml:space="preserve">
    <value>설정 안됨</value>
  </data>
  <data name="None" xml:space="preserve">
    <value>없음</value>
  </data>
  <data name="Version" xml:space="preserve">
    <value>버전</value>
  </data>
  <data name="Remove" xml:space="preserve">
    <value>제거</value>
  </data>
  <data name="StudyPlanType_Other" xml:space="preserve">
    <value>기타</value>
  </data>
  <data name="Welcome" xml:space="preserve">
    <value>환영합니다</value>
  </data>
  <data name="RowsPerPage" xml:space="preserve">
    <value>페이지당 행 수</value>
  </data>
  <data name="NoRights" xml:space="preserve">
    <value>권한 없음</value>
  </data>
  <data name="OrderStatus_Received" xml:space="preserve">
    <value>처리 중</value>
  </data>
  <data name="OrderStatus_Issued" xml:space="preserve">
    <value>고객에게 배송됨</value>
  </data>
  <data name="OrderStatus_Sent" xml:space="preserve">
    <value>사무실로 발송됨</value>
  </data>
  <data name="Amount" xml:space="preserve">
    <value>금액</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>정보</value>
  </data>
  <data name="ChangeBalance" xml:space="preserve">
    <value>잔액 변경</value>
  </data>
  <data name="MyProfile" xml:space="preserve">
    <value>내 프로필</value>
  </data>
  <data name="ResetPassword" xml:space="preserve">
    <value>비밀번호 재설정</value>
  </data>
  <data name="UserName" xml:space="preserve">
    <value>사용자 이름</value>
  </data>
  <data name="WrongPassword" xml:space="preserve">
    <value>잘못된 사용자 이름 또는 비밀번호</value>
  </data>
  <data name="PressBACKOnceAgain" xml:space="preserve">
    <value>종료하려면 뒤로가기 버튼을 한 번 더 누르세요...</value>
  </data>
  <data name="BtnShare" xml:space="preserve">
    <value>공유</value>
  </data>
  <data name="BtnGoBack" xml:space="preserve">
    <value>뒤로</value>
  </data>
  <data name="StatePaused" xml:space="preserve">
    <value>일시 정지</value>
  </data>
  <data name="BtnOk" xml:space="preserve">
    <value>확인</value>
  </data>
  <data name="BtnQuit" xml:space="preserve">
    <value>종료</value>
  </data>
  <data name="BtnContinue" xml:space="preserve">
    <value>계속</value>
  </data>
  <data name="BtnPlayAgain" xml:space="preserve">
    <value>다시 플레이</value>
  </data>
  <data name="StartGame" xml:space="preserve">
    <value>게임 시작</value>
  </data>
  <data name="Score" xml:space="preserve">
    <value>점수</value>
  </data>
  <data name="MessageGameOver" xml:space="preserve">
    <value>😔 게임 오버! 💔
최종 점수: {0}
다시 도전하시겠습니까?</value>
  </data>
  <data name="MessageLevelComplete" xml:space="preserve">
    <value>🎯 레벨 {0} 클리어! 🎉
점수: {1}
레벨 {2} 준비하세요!</value>
  </data>
  <data name="MessageWelcome" xml:space="preserve">
    <value>💥 브레이크아웃에 오신 것을 환영합니다! 🏓
🏆 모든 블록을 부수고 승리를 쟁취하세요!</value>
  </data>
  <data name="MessageWelcomeDesktop" xml:space="preserve">
    <value>💥 브레이크아웃에 오신 것을 환영합니다! 🏓
마우스/키보드로 조작하세요.
🏆 모든 블록을 부수고 승리를 쟁취하세요!</value>
  </data>
  <data name="MessageGameComplete" xml:space="preserve">
    <value>🎉 놀라워요! 모든 레벨을 정복했습니다! 🎉
최종 점수: {0}
당신은 브레이크아웃 마스터입니다!</value>
  </data>
  <data name="Lives" xml:space="preserve">
    <value>생명</value>
  </data>
  <data name="Level" xml:space="preserve">
    <value>레벨</value>
  </data>
  <data name="Lev" xml:space="preserve">
    <value>레벨</value>
  </data>
  <data name="Music" xml:space="preserve">
    <value>음악</value>
  </data>
  <data name="Sounds" xml:space="preserve">
    <value>소리</value>
  </data>
</root>